"""
Navigation Manager for Tower Defense Game Launcher

Handles view transitions and state management for the launcher interface.
"""

from typing import Optional, Dict, Any, List


class NavigationManager:
    """Manages navigation between different views and state tracking"""
    
    def __init__(self):
        """Initialize the navigation manager"""
        # State tracking
        self.current_view = "main"  # "main", "variants", or "level_options"
        self.selected_config = None
        self.selected_base_level = None  # Which base level's variants we're viewing
        self.level_options_config = None  # Config being shown in level options view
        
        # Scroll state
        self.scroll_offset = 0
        self.max_scroll = 0
        
        # UI state flags
        self.show_generation_status = False
        self.generation_message = ""
        self.show_performance_panel = False
        self.show_upgrade_menu = False
        self.show_variant_selector = False
        self.pending_variant_creation = None
        
        # Level preview system  
        self.show_level_preview = False
        self.preview_config = None
        self.preview_scroll_offset = 0
        self.preview_max_scroll = 0
    
    def navigate_to_main(self):
        """Navigate back to main view (base levels)"""
        self.current_view = "main"
        self.selected_base_level = None
        self.level_options_config = None
        self.selected_config = None
        self.scroll_offset = 0
    
    def navigate_to_variants(self, base_level_name: str):
        """Navigate to variants view for a specific base level"""
        self.current_view = "variants"
        self.selected_base_level = base_level_name
        self.selected_config = None
        self.scroll_offset = 0
    
    def navigate_to_level_options(self, config):
        """Navigate to level options view (play original vs view variants)"""
        self.current_view = "level_options"
        self.level_options_config = config
        self.selected_config = None
        self.scroll_offset = 0
    
    def show_level_preview_for_config(self, config):
        """Show level preview for a specific config"""
        self.show_level_preview = True
        self.preview_config = config
        self.preview_scroll_offset = 0
    
    def hide_level_preview(self):
        """Hide the level preview"""
        self.show_level_preview = False
        self.preview_config = None
    
    def toggle_performance_panel(self):
        """Toggle the performance panel visibility"""
        self.show_performance_panel = not self.show_performance_panel
    
    def toggle_upgrade_menu(self):
        """Toggle the upgrade menu visibility"""
        self.show_upgrade_menu = not self.show_upgrade_menu
    
    def open_variant_selector(self, config_path: str):
        """Open the variant selector for a specific config"""
        self.show_variant_selector = True
        self.pending_variant_creation = config_path
    
    def close_variant_selector(self):
        """Close the variant selector"""
        self.show_variant_selector = False
        self.pending_variant_creation = None
    
    def show_status_message(self, message: str):
        """Show a status message"""
        self.generation_message = message
        self.show_generation_status = True
    
    def hide_status_message(self):
        """Hide the status message"""
        self.show_generation_status = False
        self.generation_message = ""
    
    def update_scroll_limits(self, total_configs: int, cards_per_row: int = 3, rows_visible: int = 4):
        """Update scroll limits based on current view and config count"""
        cards_visible = cards_per_row * rows_visible
        
        if total_configs > cards_visible:
            self.max_scroll = total_configs - cards_visible
        else:
            self.max_scroll = 0
    
    def update_scroll_for_selection(self, visible_items: int = 8):
        """Update scroll offset to keep selected item visible"""
        if self.selected_config is None:
            return
        
        # If selected item is above visible area
        if self.selected_config < self.scroll_offset:
            self.scroll_offset = self.selected_config
        
        # If selected item is below visible area
        elif self.selected_config >= self.scroll_offset + visible_items:
            self.scroll_offset = self.selected_config - visible_items + 1
        
        # Clamp scroll offset
        self.scroll_offset = max(0, min(self.max_scroll, self.scroll_offset))
    
    def scroll_up(self):
        """Scroll up by one item"""
        self.scroll_offset = max(0, self.scroll_offset - 1)
    
    def scroll_down(self):
        """Scroll down by one item"""
        self.scroll_offset = min(self.max_scroll, self.scroll_offset + 1)
    
    def preview_scroll_up(self):
        """Scroll up in preview by one item"""
        self.preview_scroll_offset = max(0, self.preview_scroll_offset - 1)
    
    def preview_scroll_down(self):
        """Scroll down in preview by one item"""
        self.preview_scroll_offset = min(self.preview_max_scroll, self.preview_scroll_offset + 1)
    
    def select_config(self, index: int):
        """Select a configuration by index"""
        self.selected_config = index
    
    def move_selection_up(self, max_configs: int):
        """Move selection up"""
        if self.selected_config is None:
            self.selected_config = 0
        else:
            self.selected_config = max(0, self.selected_config - 1)
        self.update_scroll_for_selection()
    
    def move_selection_down(self, max_configs: int):
        """Move selection down"""
        if self.selected_config is None:
            self.selected_config = 0
        else:
            self.selected_config = min(max_configs - 1, self.selected_config + 1)
        self.update_scroll_for_selection()
    
    def get_current_view_title(self, selected_base_level: str = None, level_options_config: Dict = None) -> tuple:
        """Get title and subtitle for current view"""
        if self.current_view == "main":
            title = "Tower Defense"
            subtitle = "Select a level to configure"
        elif self.current_view == "level_options":
            level_name = level_options_config.get('name', 'Unknown Level') if level_options_config else 'Unknown'
            title = level_name
            subtitle = "Choose how to play this level"
        else:  # variants view
            base_level_display = (selected_base_level or "Unknown").replace('_', ' ').title()
            title = f"{base_level_display} - Variants"
            subtitle = "Select a variant to play"
        
        return title, subtitle
    
    def should_show_back_button(self) -> bool:
        """Check if back button should be shown"""
        return self.current_view in ["variants", "level_options"]
    
    def get_current_config_list(self, configs: List[Dict], variants: Dict[str, List[Dict]]) -> List[Dict]:
        """Get the current list of configs to display based on view"""
        if self.current_view == "main":
            return configs
        else:  # variants view
            base_level = self.selected_base_level
            return variants.get(base_level, [])
    
    def reset_state(self):
        """Reset navigation state to defaults"""
        self.current_view = "main"
        self.selected_config = None
        self.selected_base_level = None
        self.level_options_config = None
        self.scroll_offset = 0
        self.max_scroll = 0
        self.show_generation_status = False
        self.generation_message = ""
        self.show_performance_panel = False
        self.show_upgrade_menu = False
        self.show_variant_selector = False
        self.pending_variant_creation = None
        self.show_level_preview = False
        self.preview_config = None
        self.preview_scroll_offset = 0
        self.preview_max_scroll = 0
