"""
Event Handler for Tower Defense Game Launcher

Manages all user input events including mouse clicks, keyboard input, and scrolling.
"""

import pygame
import time
from typing import Optional, Callable, Dict, Any, List


class EventHandler:
    """Handles all user input events for the launcher"""
    
    def __init__(self, navigation_manager, configuration_manager):
        """Initialize the event handler"""
        self.navigation_manager = navigation_manager
        self.configuration_manager = configuration_manager
        
        # Visual feedback state tracking
        self.pressed_button = None  # Track which button is currently pressed
        self.pressed_card = None    # Track which card is currently pressed
        self.button_press_time = 0  # Time when button was pressed
        self.card_press_time = 0    # Time when card was pressed
        
        # Button coordinates for click detection (set by renderer)
        self.button_coords = {}
        
        # Callbacks for specific actions
        self.callbacks = {
            'generate_config': None,
            'launch_game': None,
            'handle_variant_creation': None,
            'quit_application': None
        }
    
    def set_callback(self, action: str, callback: Callable):
        """Set a callback function for a specific action"""
        if action in self.callbacks:
            self.callbacks[action] = callback
    
    def set_button_coordinates(self, coords: Dict[str, tuple]):
        """Set button coordinates for click detection"""
        self.button_coords = coords
    
    def handle_events(self, events: List[pygame.event.Event]) -> bool:
        """Handle all pygame events. Returns False if application should quit."""
        for event in events:
            if event.type == pygame.QUIT:
                return False
            
            elif event.type == pygame.KEYDOWN:
                if not self._handle_keyboard_event(event):
                    return False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self._handle_mouse_button_down(event)
            
            elif event.type == pygame.MOUSEWHEEL:
                self._handle_mouse_wheel(event)
        
        return True
    
    def _handle_keyboard_event(self, event) -> bool:
        """Handle keyboard events. Returns False if application should quit."""
        if event.key == pygame.K_UP:
            self._handle_up_key()
        elif event.key == pygame.K_DOWN:
            self._handle_down_key()
        elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
            self._handle_enter_key()
        elif event.key == pygame.K_ESCAPE:
            return self._handle_escape_key()
        elif event.key == pygame.K_g:
            self._handle_g_key()
        elif event.key == pygame.K_p:
            self._handle_p_key()
        elif event.key == pygame.K_v:
            self._handle_v_key()
        
        return True
    
    def _handle_up_key(self):
        """Handle up arrow key"""
        if self.navigation_manager.show_level_preview:
            self.navigation_manager.preview_scroll_up()
        elif self.navigation_manager.selected_config is None:
            self.navigation_manager.selected_config = 0
        else:
            current_configs = self.navigation_manager.get_current_config_list(
                self.configuration_manager.configs, 
                self.configuration_manager.variants
            )
            self.navigation_manager.move_selection_up(len(current_configs))
    
    def _handle_down_key(self):
        """Handle down arrow key"""
        if self.navigation_manager.show_level_preview:
            self.navigation_manager.preview_scroll_down()
        elif self.navigation_manager.selected_config is None:
            self.navigation_manager.selected_config = 0
        else:
            current_configs = self.navigation_manager.get_current_config_list(
                self.configuration_manager.configs, 
                self.configuration_manager.variants
            )
            self.navigation_manager.move_selection_down(len(current_configs))
    
    def _handle_enter_key(self):
        """Handle enter/space key"""
        if self.navigation_manager.show_level_preview and self.navigation_manager.preview_config:
            # Launch game from preview
            if self.callbacks['launch_game']:
                self.callbacks['launch_game'](self.navigation_manager.preview_config)
        elif self.navigation_manager.current_view == "level_options" and self.navigation_manager.level_options_config:
            # In level options view, enter/space triggers "Play Original"
            self.navigation_manager.show_level_preview_for_config(self.navigation_manager.level_options_config)
        else:
            # Handle config selection
            current_configs = self.navigation_manager.get_current_config_list(
                self.configuration_manager.configs, 
                self.configuration_manager.variants
            )
            if (self.navigation_manager.selected_config is not None and 
                0 <= self.navigation_manager.selected_config < len(current_configs)):
                config = current_configs[self.navigation_manager.selected_config]
                if self.navigation_manager.current_view == "main":
                    # Main menu card selected - open level options
                    self.navigation_manager.navigate_to_level_options(config)
                else:
                    # Variant in variants view - show preview
                    self.navigation_manager.show_level_preview_for_config(config)
    
    def _handle_escape_key(self) -> bool:
        """Handle escape key. Returns False if application should quit."""
        if self.navigation_manager.show_variant_selector:
            self.navigation_manager.close_variant_selector()
        elif self.navigation_manager.show_level_preview:
            self.navigation_manager.hide_level_preview()
        elif self.navigation_manager.show_upgrade_menu:
            self.navigation_manager.show_upgrade_menu = False
        elif self.navigation_manager.current_view == "level_options":
            # Go back to main view from level options
            self.navigation_manager.navigate_to_main()
        elif self.navigation_manager.current_view == "variants":
            # Go back to main view from variants
            self.navigation_manager.navigate_to_main()
        else:
            return False  # Quit application
        
        return True
    
    def _handle_g_key(self):
        """Handle 'g' key for generating new config"""
        if (not self.navigation_manager.show_level_preview and 
            not self.navigation_manager.show_upgrade_menu):
            # Generate new adaptive config from recent games
            if self.callbacks['generate_config']:
                self.callbacks['generate_config']()
    
    def _handle_p_key(self):
        """Handle 'p' key for performance panel"""
        if (not self.navigation_manager.show_level_preview and 
            not self.navigation_manager.show_upgrade_menu and 
            not self.navigation_manager.show_variant_selector):
            # Toggle performance panel
            self.navigation_manager.toggle_performance_panel()
    
    def _handle_v_key(self):
        """Handle 'v' key for variant creation"""
        if (not self.navigation_manager.show_level_preview and 
            not self.navigation_manager.show_upgrade_menu and 
            not self.navigation_manager.show_variant_selector):
            # Open variant selector for selected level
            if (self.navigation_manager.selected_config is not None and 
                0 <= self.navigation_manager.selected_config < len(self.configuration_manager.configs)):
                config_info = self.configuration_manager.configs[self.navigation_manager.selected_config]
                self.navigation_manager.open_variant_selector(config_info['path'])
    
    def _handle_mouse_button_down(self, event):
        """Handle mouse button down events"""
        if event.button == 1:  # Left click
            self._handle_left_click(event.pos)
        elif event.button == 4:  # Mouse wheel up
            self._handle_scroll_up()
        elif event.button == 5:  # Mouse wheel down
            self._handle_scroll_down()
    
    def _handle_mouse_wheel(self, event):
        """Handle mouse wheel events"""
        if self.navigation_manager.show_variant_selector:
            # Let variant UI handle scrolling
            pass  # This will be handled by the variant UI
        elif self.navigation_manager.show_level_preview:
            if event.y > 0:
                self.navigation_manager.preview_scroll_up()
            else:
                self.navigation_manager.preview_scroll_down()
        elif self.navigation_manager.show_upgrade_menu:
            # Let upgrade UI handle scrolling
            pass  # This will be handled by the upgrade UI
        else:
            if event.y > 0:
                self.navigation_manager.scroll_up()
            else:
                self.navigation_manager.scroll_down()
    
    def _handle_scroll_up(self):
        """Handle scroll up (mouse wheel up)"""
        if self.navigation_manager.show_variant_selector:
            # Let variant UI handle scrolling
            pass
        elif self.navigation_manager.show_level_preview:
            self.navigation_manager.preview_scroll_up()
        elif self.navigation_manager.show_upgrade_menu:
            # Let upgrade UI handle scrolling
            pass
        else:
            self.navigation_manager.scroll_up()
    
    def _handle_scroll_down(self):
        """Handle scroll down (mouse wheel down)"""
        if self.navigation_manager.show_variant_selector:
            # Let variant UI handle scrolling
            pass
        elif self.navigation_manager.show_level_preview:
            self.navigation_manager.preview_scroll_down()
        elif self.navigation_manager.show_upgrade_menu:
            # Let upgrade UI handle scrolling
            pass
        else:
            self.navigation_manager.scroll_down()
    
    def _handle_left_click(self, pos):
        """Handle left mouse click"""
        x, y = pos
        
        # Check if status message is showing and dismiss it
        if self.navigation_manager.show_generation_status and self.navigation_manager.generation_message:
            self.navigation_manager.hide_status_message()
            return
        
        # Handle different UI overlays first
        if self.navigation_manager.show_variant_selector:
            # Let variant UI handle the click
            return  # This will be handled by the variant UI
        elif self.navigation_manager.show_level_preview:
            self._handle_preview_click(pos)
            return
        elif self.navigation_manager.show_upgrade_menu:
            # Let upgrade UI handle the click
            return  # This will be handled by the upgrade UI
        
        # Check button clicks
        if self._handle_button_click(pos):
            return
        
        # Handle navigation buttons for different views
        if self.navigation_manager.current_view == "variants":
            if self._handle_back_button_click(pos):
                return
        elif self.navigation_manager.current_view == "level_options":
            self._handle_level_options_click(pos)
            return
        
        # Handle card clicks
        self._handle_card_click(pos)

    def _handle_button_click(self, pos) -> bool:
        """Handle button clicks. Returns True if a button was clicked."""
        x, y = pos

        if not self.button_coords:
            return False

        # Generate button
        if 'generate' in self.button_coords:
            btn_x, btn_y, btn_w, btn_h = self.button_coords['generate']
            if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                self.pressed_button = 'generate'
                self.button_press_time = time.time()

                self.navigation_manager.show_status_message("🔄 Generating new level...")

                if self.callbacks['generate_config']:
                    self.callbacks['generate_config']()
                return True

        # Stats button
        if 'stats' in self.button_coords:
            btn_x, btn_y, btn_w, btn_h = self.button_coords['stats']
            if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                self.pressed_button = 'stats'
                self.button_press_time = time.time()

                self.navigation_manager.toggle_performance_panel()
                return True

        # Upgrade button
        if 'upgrades' in self.button_coords:
            btn_x, btn_y, btn_w, btn_h = self.button_coords['upgrades']
            if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                self.pressed_button = 'upgrades'
                self.button_press_time = time.time()

                self.navigation_manager.toggle_upgrade_menu()
                return True

        # Variant button (if enabled)
        if 'variants' in self.button_coords:
            btn_x, btn_y, btn_w, btn_h = self.button_coords['variants']
            if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                self.pressed_button = 'variants'
                self.button_press_time = time.time()
                if self.callbacks['handle_variant_creation']:
                    self.callbacks['handle_variant_creation']()
                return True

        return False

    def _handle_back_button_click(self, pos) -> bool:
        """Handle back button click in variants view. Returns True if clicked."""
        x, y = pos
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35
        if back_btn_x <= x <= back_btn_x + back_btn_width and back_btn_y <= y <= back_btn_y + back_btn_height:
            self.navigation_manager.navigate_to_main()
            return True
        return False

    def _handle_preview_click(self, pos):
        """Handle mouse clicks in the level preview screen"""
        x, y = pos

        # Check for back button click (top-left)
        if 50 <= x <= 150 and 50 <= y <= 80:
            self.navigation_manager.hide_level_preview()
            return

        # Check for play button click (center-bottom) - match the drawing coordinates exactly
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = 500  # This should match the renderer's coordinates
        play_btn_y = 680  # This should match the renderer's coordinates
        if play_btn_x <= x <= play_btn_x + play_btn_width and play_btn_y <= y <= play_btn_y + play_btn_height:
            if self.navigation_manager.preview_config and self.callbacks['launch_game']:
                self.callbacks['launch_game'](self.navigation_manager.preview_config)
            return

    def _handle_level_options_click(self, pos):
        """Handle clicks in the level options view"""
        x, y = pos
        if not self.navigation_manager.level_options_config:
            return

        # Back button
        if self._handle_back_button_click(pos):
            return

        # Calculate button positions - MUST MATCH draw_level_options() coordinates!
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = 500  # Screen width // 2

        # Level info card coordinates
        card_height = 200
        card_y = 180
        buttons_y = card_y + card_height + 50

        # "Play Original" button
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        if (play_btn_x <= x <= play_btn_x + button_width and
            play_btn_y <= y <= play_btn_y + button_height):
            # Launch the original level
            self.navigation_manager.show_level_preview_for_config(self.navigation_manager.level_options_config)
            return

        # "View Variants" button (only show if variants exist)
        base_level = self.configuration_manager.get_base_level_for_config(self.navigation_manager.level_options_config)
        has_variants = base_level in self.configuration_manager.variants and len(self.configuration_manager.variants[base_level]) > 0

        current_button_y = buttons_y + button_height + button_spacing

        if has_variants:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            if (variants_btn_x <= x <= variants_btn_x + button_width and
                variants_btn_y <= y <= variants_btn_y + button_height):
                # Navigate to variants view
                self.navigation_manager.navigate_to_variants(base_level)
                return

            current_button_y += button_height + button_spacing

        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y
        if (create_variant_btn_x <= x <= create_variant_btn_x + button_width and
            create_variant_btn_y <= y <= create_variant_btn_y + button_height):
            # Open variant creation UI
            self.navigation_manager.open_variant_selector(self.navigation_manager.level_options_config['path'])
            return

    def _handle_card_click(self, pos):
        """Handle card clicks in main and variants views"""
        x, y = pos

        current_configs = self.navigation_manager.get_current_config_list(
            self.configuration_manager.configs,
            self.configuration_manager.variants
        )

        if not current_configs:
            return

        card_width = 250
        card_height = 120
        padding = 20
        margin = 40
        header_height = 100
        content_y = header_height + 20

        # In variants view, leave space for back button
        if self.navigation_manager.current_view == "variants":
            content_y += 50

        # Calculate grid layout
        cards_per_row = (1000 - 2 * margin) // (card_width + padding)  # Assuming screen width 1000

        # Check if click is in the card area
        if y >= content_y:
            # Calculate which card was clicked
            relative_x = x - margin
            relative_y = y - content_y

            if relative_x >= 0 and relative_y >= 0:
                col = relative_x // (card_width + padding)
                row = relative_y // (card_height + padding)

                # Check if click is within a card boundary
                card_x = col * (card_width + padding)
                card_y = row * (card_height + padding)

                if (card_x <= relative_x <= card_x + card_width and
                    card_y <= relative_y <= card_y + card_height):

                    clicked_index = row * cards_per_row + col + self.navigation_manager.scroll_offset

                    if 0 <= clicked_index < len(current_configs):
                        config = current_configs[clicked_index]

                        # Track card press for visual feedback
                        self.pressed_card = clicked_index
                        self.card_press_time = time.time()

                        # Check if it's a play button click (only in variants view)
                        if self.navigation_manager.current_view == "variants":
                            play_btn_x = card_x + card_width - 60
                            play_btn_y = card_y + card_height - 35

                            if (play_btn_x <= relative_x <= play_btn_x + 45 and
                                play_btn_y <= relative_y <= play_btn_y + 25):
                                # Play button clicked - show preview
                                self.navigation_manager.show_level_preview_for_config(config)
                                return

                        # Card clicked (not play button)
                        if self.navigation_manager.current_view == "main":
                            # Main menu card clicked - open level options
                            self.navigation_manager.navigate_to_level_options(config)
                        elif self.navigation_manager.current_view == "variants":
                            # Variant card clicked (not play button) - show preview directly
                            self.navigation_manager.show_level_preview_for_config(config)
                        else:
                            # Other views - just select
                            self.navigation_manager.select_config(clicked_index)

    def clear_visual_feedback(self):
        """Clear visual feedback states after brief duration"""
        current_time = time.time()
        if self.pressed_button and current_time - self.button_press_time > 0.3:
            self.pressed_button = None
        if self.pressed_card and current_time - self.card_press_time > 0.3:
            self.pressed_card = None

    def get_pressed_button(self) -> Optional[str]:
        """Get currently pressed button for visual feedback"""
        return self.pressed_button

    def get_pressed_card(self) -> Optional[int]:
        """Get currently pressed card for visual feedback"""
        return self.pressed_card
