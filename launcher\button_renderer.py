"""
Button Renderer for Tower Defense Game Launcher

Handles rendering of action buttons and navigation elements.
"""

import pygame
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Optional


class ButtonRenderer:
    """Renders action buttons and navigation elements"""
    
    def __init__(self, screen_width: int, screen_height: int, colors: Dict[str, Tuple[int, int, int]], fonts: Dict[str, pygame.font.Font]):
        """Initialize the button renderer"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.colors = colors
        self.fonts = fonts
        
        # Button coordinates for click detection
        self.button_coords = {}
    
    def draw_action_buttons(self, screen: pygame.Surface, pressed_button: Optional[str] = None) -> Dict[str, Tuple[int, int, int, int]]:
        """Draw modern action buttons at the bottom"""
        button_area_height = 80
        button_y = self.screen_height - button_area_height
        
        # Background for button area
        pygame.draw.rect(screen, self.colors['CARD_BG'], (0, button_y, self.screen_width, button_area_height))
        pygame.draw.line(screen, self.colors['BORDER_LIGHT'], (0, button_y), (self.screen_width, button_y), 1)
        
        # Button specifications
        button_height = 35
        button_y_pos = button_y + 22
        button_spacing = 20
        
        # Calculate button positions (centered) - 3 buttons now
        btn1_width = 200  # Generate New Level
        btn2_width = 120  # Stats
        btn3_width = 120  # Upgrades
        total_width = btn1_width + btn2_width + btn3_width + button_spacing * 2
        start_x = (self.screen_width - total_width) // 2
        
        # Generate button (primary)
        btn1_x = start_x
        is_pressed = pressed_button == 'generate'
        btn_color = (int(self.colors['ACCENT_BLUE'][0] * 0.8), int(self.colors['ACCENT_BLUE'][1] * 0.8), int(self.colors['ACCENT_BLUE'][2] * 0.8)) if is_pressed else self.colors['ACCENT_BLUE']
        
        pygame.draw.rect(screen, btn_color, (btn1_x, button_y_pos, btn1_width, button_height), border_radius=8)
        
        btn1_text = self.fonts['info'].render("Generate New Level", True, self.colors['CARD_BG'])
        offset = 2 if is_pressed else 0
        btn1_rect = btn1_text.get_rect(center=(btn1_x + btn1_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn1_text, btn1_rect)
        
        # Performance/Stats button
        btn2_x = btn1_x + btn1_width + button_spacing
        is_pressed = pressed_button == 'stats'
        perf_color = (int(self.colors['ACCENT_GREEN'][0] * 0.8), int(self.colors['ACCENT_GREEN'][1] * 0.8), int(self.colors['ACCENT_GREEN'][2] * 0.8)) if is_pressed else self.colors['ACCENT_GREEN']
        
        pygame.draw.rect(screen, perf_color, (btn2_x, button_y_pos, btn2_width, button_height), border_radius=8)
        
        # Show different style if performance panel is not showing
        show_performance_panel = False  # This should be passed as parameter
        if not show_performance_panel and not is_pressed:
            pygame.draw.rect(screen, self.colors['CARD_BG'], (btn2_x + 1, button_y_pos + 1, btn2_width - 2, button_height - 2), border_radius=7)
        
        offset = 2 if is_pressed else 0
        btn2_text = self.fonts['info'].render("Stats", True, self.colors['CARD_BG'] if show_performance_panel or is_pressed else self.colors['TEXT_PRIMARY'])
        btn2_rect = btn2_text.get_rect(center=(btn2_x + btn2_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn2_text, btn2_rect)
        
        # Upgrade button
        btn3_x = btn2_x + btn2_width + button_spacing
        is_pressed = pressed_button == 'upgrades'
        upgrade_color = (int(self.colors['ACCENT_ORANGE'][0] * 0.8), int(self.colors['ACCENT_ORANGE'][1] * 0.8), int(self.colors['ACCENT_ORANGE'][2] * 0.8)) if is_pressed else self.colors['ACCENT_ORANGE']
        
        pygame.draw.rect(screen, upgrade_color, (btn3_x, button_y_pos, btn3_width, button_height), border_radius=8)
        
        # Show different style if upgrade menu is not showing
        show_upgrade_menu = False  # This should be passed as parameter
        if not show_upgrade_menu and not is_pressed:
            pygame.draw.rect(screen, self.colors['CARD_BG'], (btn3_x + 1, button_y_pos + 1, btn3_width - 2, button_height - 2), border_radius=7)
        
        offset = 2 if is_pressed else 0
        btn3_text = self.fonts['info'].render("Upgrades", True, self.colors['CARD_BG'] if show_upgrade_menu or is_pressed else self.colors['TEXT_PRIMARY'])
        btn3_rect = btn3_text.get_rect(center=(btn3_x + btn3_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn3_text, btn3_rect)
        
        # Store button coordinates for click detection
        self.button_coords = {
            'generate': (btn1_x, button_y_pos, btn1_width, button_height),
            'stats': (btn2_x, button_y_pos, btn2_width, button_height),
            'upgrades': (btn3_x, button_y_pos, btn3_width, button_height)
        }
        
        return self.button_coords
    
    def draw_back_button(self, screen: pygame.Surface):
        """Draw back button in variants view"""
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35
        
        # Button background
        pygame.draw.rect(screen, self.colors['BORDER_DARK'], (back_btn_x, back_btn_y, back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(screen, self.colors['CARD_BG'], (back_btn_x + 1, back_btn_y + 1, back_btn_width - 2, back_btn_height - 2), border_radius=7)
        
        # Button text
        back_text = self.fonts['info'].render("← Back", True, self.colors['TEXT_PRIMARY'])
        back_rect = back_text.get_rect(center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        screen.blit(back_text, back_rect)
    
    def draw_level_options_buttons(self, screen: pygame.Surface, config, variants_count: int = 0):
        """Draw buttons for level options view"""
        # Calculate button positions
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = self.screen_width // 2
        
        # Level info card coordinates
        card_height = 200
        card_y = 180
        buttons_y = card_y + card_height + 50
        
        # "Play Original" button
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        pygame.draw.rect(screen, self.colors['ACCENT_GREEN'], (play_btn_x, play_btn_y, button_width, button_height), border_radius=25)
        
        play_text = self.fonts['subtitle'].render("Play Original", True, self.colors['CARD_BG'])
        play_rect = play_text.get_rect(center=(play_btn_x + button_width // 2, play_btn_y + button_height // 2))
        screen.blit(play_text, play_rect)
        
        current_button_y = buttons_y + button_height + button_spacing
        
        # "View Variants" button (only if variants exist)
        if variants_count > 0:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            
            pygame.draw.rect(screen, self.colors['ACCENT_BLUE'], (variants_btn_x, variants_btn_y, button_width, button_height), border_radius=25)
            
            variants_text = self.fonts['subtitle'].render(f"View Variants ({variants_count})", True, self.colors['CARD_BG'])
            variants_rect = variants_text.get_rect(center=(variants_btn_x + button_width // 2, variants_btn_y + button_height // 2))
            screen.blit(variants_text, variants_rect)
            
            current_button_y += button_height + button_spacing
        
        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y
        
        pygame.draw.rect(screen, (138, 43, 226), (create_variant_btn_x, create_variant_btn_y, button_width, button_height), border_radius=25)
        
        create_variant_text = self.fonts['subtitle'].render("Create Variant", True, self.colors['CARD_BG'])
        create_variant_rect = create_variant_text.get_rect(center=(create_variant_btn_x + button_width // 2, create_variant_btn_y + button_height // 2))
        screen.blit(create_variant_text, create_variant_rect)
        
        # Instructions
        instructions_y = current_button_y + button_height + 20
        
        instructions = "Click a button above to proceed, or use the back button to return to the main menu"
        instr_text = self.fonts['small'].render(instructions, True, self.colors['TEXT_SECONDARY'])
        instr_rect = instr_text.get_rect(center=(self.screen_width // 2, instructions_y))
        screen.blit(instr_text, instr_rect)
    
    def draw_preview_buttons(self, screen: pygame.Surface):
        """Draw buttons for level preview screen"""
        # Back button (top-left)
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 30
        pygame.draw.rect(screen, self.colors['BORDER_DARK'], (back_btn_x, back_btn_y, back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(screen, self.colors['CARD_BG'], (back_btn_x + 1, back_btn_y + 1, back_btn_width - 2, back_btn_height - 2), border_radius=7)
        back_text = self.fonts['info'].render("← Back", True, self.colors['TEXT_PRIMARY'])
        back_rect = back_text.get_rect(center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        screen.blit(back_text, back_rect)
        
        # Play button (large, centered at bottom)
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = self.screen_width // 2 - play_btn_width // 2
        play_btn_y = self.screen_height - 120
        
        pygame.draw.rect(screen, self.colors['ACCENT_GREEN'], (play_btn_x, play_btn_y, play_btn_width, play_btn_height), border_radius=25)
        
        # Play button text
        play_text = self.fonts['subtitle'].render("START GAME", True, self.colors['CARD_BG'])
        play_rect = play_text.get_rect(center=(play_btn_x + play_btn_width // 2, play_btn_y + play_btn_height // 2))
        screen.blit(play_text, play_rect)
    
    def get_button_coordinates(self) -> Dict[str, Tuple[int, int, int, int]]:
        """Get button coordinates for click detection"""
        return self.button_coords
