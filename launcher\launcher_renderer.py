"""
Main Launcher Renderer for Tower Defense Game Launcher

Coordinates all rendering components and manages the main drawing loop.
"""

import pygame
from typing import Dict, Tuple, Optional, List
from .card_renderer import <PERSON><PERSON>enderer
from .button_renderer import Button<PERSON>enderer
from .status_renderer import StatusRenderer


class LauncherRenderer:
    """Main renderer that coordinates all UI rendering"""
    
    def __init__(self, screen_width: int, screen_height: int):
        """Initialize the launcher renderer"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Define color scheme (modern, clean)
        self.colors = {
            'BACKGROUND': (32, 34, 40),      # Dark modern background
            'CARD_BG': (48, 52, 64),         # Card background
            'HOVER_BG': (56, 60, 74),        # Hover background
            'SELECTED_BG': (64, 68, 84),     # Selected background
            'TEXT_PRIMARY': (240, 242, 247), # Primary text
            'TEXT_SECONDARY': (155, 164, 181), # Secondary text
            'ACCENT_BLUE': (59, 130, 246),   # Primary action color
            'ACCENT_GREEN': (34, 197, 94),   # Success color
            'ACCENT_ORANGE': (251, 146, 60), # Warning color
            'BORDER_LIGHT': (75, 85, 99),    # Light border
            'BORDER_DARK': (31, 41, 55),     # Dark border
        }
        
        # Initialize fonts
        self.fonts = {
            'title': pygame.font.Font(None, 42),
            'subtitle': pygame.font.Font(None, 28),
            'menu': pygame.font.Font(None, 24),
            'info': pygame.font.Font(None, 20),
            'small': pygame.font.Font(None, 16)
        }
        
        # Initialize sub-renderers
        self.card_renderer = CardRenderer(screen_width, screen_height, self.colors, self.fonts)
        self.button_renderer = ButtonRenderer(screen_width, screen_height, self.colors, self.fonts)
        self.status_renderer = StatusRenderer(screen_width, screen_height, self.colors, self.fonts)
    
    def draw_main_interface(self, screen: pygame.Surface, navigation_manager, configuration_manager, 
                           event_handler, performance_stats: Optional[Dict] = None):
        """Draw the main launcher interface"""
        # Soft background
        screen.fill(self.colors['BACKGROUND'])
        
        # Show level preview if active
        if navigation_manager.show_level_preview and navigation_manager.preview_config:
            self.draw_level_preview(screen, navigation_manager.preview_config, navigation_manager)
            return
        
        # Header section
        self.draw_header(screen, navigation_manager, configuration_manager)
        
        # Draw back button in variants view or level options view
        if navigation_manager.should_show_back_button():
            self.button_renderer.draw_back_button(screen)
        
        # Action buttons (draw in ALL views)
        button_coords = self.button_renderer.draw_action_buttons(screen, event_handler.get_pressed_button())
        event_handler.set_button_coordinates(button_coords)
        
        # Show level options view if active
        if navigation_manager.current_view == "level_options":
            self.draw_level_options(screen, navigation_manager, configuration_manager)
        else:
            # Main content area (only for main and variants views)
            self.draw_main_content(screen, navigation_manager, configuration_manager, event_handler)
        
        # Performance panel (if visible)
        if navigation_manager.show_performance_panel:
            if performance_stats:
                self.status_renderer.draw_performance_panel(screen, performance_stats)
            else:
                self.status_renderer.draw_no_performance_data_message(screen)
        
        # Status message
        if navigation_manager.show_generation_status and navigation_manager.generation_message:
            self.status_renderer.draw_status_message(screen, navigation_manager.generation_message)
    
    def draw_header(self, screen: pygame.Surface, navigation_manager, configuration_manager):
        """Draw the header section with title and subtitle"""
        header_height = 100
        pygame.draw.rect(screen, self.colors['CARD_BG'], (0, 0, self.screen_width, header_height))
        pygame.draw.line(screen, self.colors['BORDER_LIGHT'], (0, header_height), (self.screen_width, header_height), 1)
        
        # Get title and subtitle based on current view
        if navigation_manager.current_view == "main":
            title_text = self.fonts['title'].render("Tower Defense", True, self.colors['TEXT_PRIMARY'])
            current_configs = navigation_manager.get_current_config_list(
                configuration_manager.configs, configuration_manager.variants)
            if current_configs:
                subtitle_text = self.fonts['info'].render("Select a level to configure", True, self.colors['TEXT_SECONDARY'])
            else:
                subtitle_text = self.fonts['info'].render("No levels found", True, self.colors['ACCENT_ORANGE'])
        elif navigation_manager.current_view == "level_options":
            # Show level options view
            level_name = navigation_manager.level_options_config.get('name', 'Unknown Level') if navigation_manager.level_options_config else 'Unknown'
            title_text = self.fonts['title'].render(level_name, True, self.colors['TEXT_PRIMARY'])
            subtitle_text = self.fonts['info'].render("Choose how to play this level", True, self.colors['TEXT_SECONDARY'])
        else:  # variants view
            # Show base level name with indicator this is variants view
            base_level_display = (navigation_manager.selected_base_level or "Unknown").replace('_', ' ').title()
            title_text = self.fonts['title'].render(f"{base_level_display} - Variants", True, self.colors['TEXT_PRIMARY'])
            variant_count = len(configuration_manager.variants.get(navigation_manager.selected_base_level or "", []))
            subtitle_text = self.fonts['info'].render(f"{variant_count} variants available", True, self.colors['TEXT_SECONDARY'])
        
        title_rect = title_text.get_rect(center=(self.screen_width // 2, 35))
        screen.blit(title_text, title_rect)
        subtitle_rect = subtitle_text.get_rect(center=(self.screen_width // 2, 65))
        screen.blit(subtitle_text, subtitle_rect)
    
    def draw_main_content(self, screen: pygame.Surface, navigation_manager, configuration_manager, event_handler):
        """Draw main content area for main and variants views"""
        header_height = 100
        content_y = header_height + 20
        content_height = self.screen_height - header_height - 120  # Leave space for buttons
        
        # In variants view, leave space for back button
        if navigation_manager.current_view == "variants":
            content_y += 50
            content_height -= 50
        
        current_configs = navigation_manager.get_current_config_list(
            configuration_manager.configs, configuration_manager.variants)
        if current_configs:
            self.card_renderer.draw_level_cards(
                screen, current_configs, content_y, content_height,
                navigation_manager.scroll_offset, navigation_manager.selected_config,
                event_handler.get_pressed_card(), navigation_manager.current_view,
                configuration_manager.variants, configuration_manager
            )
    
    def draw_level_options(self, screen: pygame.Surface, navigation_manager, configuration_manager):
        """Draw the level options view"""
        if not navigation_manager.level_options_config:
            return
        
        # Draw level info card
        self.card_renderer.draw_level_info_card(screen, navigation_manager.level_options_config)
        
        # Draw action buttons
        base_level = configuration_manager.get_base_level_for_config(navigation_manager.level_options_config)
        variants_count = len(configuration_manager.variants.get(base_level, []))
        self.button_renderer.draw_level_options_buttons(screen, navigation_manager.level_options_config, variants_count)
    
    def draw_level_preview(self, screen: pygame.Surface, preview_config: Dict, navigation_manager):
        """Draw the level preview screen with description and rewards"""
        # Background
        screen.fill(self.colors['BACKGROUND'])
        
        # Get level metadata
        metadata = preview_config.get('level_metadata', {})
        
        # Check if this is a variant
        is_variant = '_variant_metadata' in preview_config
        variant_metadata = preview_config.get('_variant_metadata', {}) if is_variant else {}
        
        # Header section
        header_height = 100
        pygame.draw.rect(screen, self.colors['CARD_BG'], (0, 0, self.screen_width, header_height))
        pygame.draw.line(screen, self.colors['BORDER_LIGHT'], (0, header_height), (self.screen_width, header_height), 1)
        
        # Draw preview buttons
        self.button_renderer.draw_preview_buttons(screen)
        
        # Level title
        if is_variant:
            level_name = variant_metadata.get('variant_name', preview_config.get('name', 'Unknown Variant'))
        else:
            level_name = metadata.get('name', preview_config.get('name', 'Unknown Level'))
        title_text = self.fonts['title'].render(level_name, True, self.colors['TEXT_PRIMARY'])
        title_rect = title_text.get_rect(center=(self.screen_width // 2, 35))
        screen.blit(title_text, title_rect)
        
        # Difficulty rating and variant indicator
        if is_variant:
            base_level = variant_metadata.get('base_level', 'Unknown')
            difficulty_mult = variant_metadata.get('difficulty_multiplier', 1.0)
            reward_mult = variant_metadata.get('reward_multiplier', 1.0)
            difficulty_text = self.fonts['info'].render(f"Variant of {base_level} • {difficulty_mult:.1f}x difficulty • {reward_mult:.1f}x reward", True, (138, 43, 226))
        else:
            difficulty_rating = metadata.get('difficulty_rating', 'Unknown')
            difficulty_color = self.colors['ACCENT_GREEN'] if difficulty_rating == 'Easy' else self.colors['ACCENT_ORANGE'] if difficulty_rating == 'Normal' else (220, 53, 69)
            difficulty_text = self.fonts['info'].render(f"Difficulty: {difficulty_rating}", True, difficulty_color)
        
        difficulty_rect = difficulty_text.get_rect(center=(self.screen_width // 2, 65))
        screen.blit(difficulty_text, difficulty_rect)
        
        # Draw preview content (description, details, rewards, etc.)
        self.draw_preview_content(screen, preview_config, metadata, is_variant, variant_metadata)
    
    def draw_preview_content(self, screen: pygame.Surface, preview_config: Dict, metadata: Dict, 
                           is_variant: bool, variant_metadata: Dict):
        """Draw the main content of the level preview"""
        # Main content area
        content_margin = 80
        content_width = self.screen_width - 2 * content_margin
        content_x = content_margin
        content_y = 140  # After header
        
        # Description section
        if is_variant:
            desc_title = self.fonts['subtitle'].render("Variant Modifiers", True, self.colors['TEXT_PRIMARY'])
            screen.blit(desc_title, (content_x, content_y))
            content_y += 35
            
            # Show modifiers applied
            modifiers = variant_metadata.get('modifiers', [])
            if modifiers:
                for modifier in modifiers:
                    mod_name = modifier.get('name', 'Unknown Modifier')
                    mod_desc = modifier.get('description', 'No description')
                    
                    # Modifier name
                    mod_text = self.fonts['menu'].render(f"• {mod_name}", True, self.colors['TEXT_PRIMARY'])
                    screen.blit(mod_text, (content_x + 20, content_y))
                    content_y += 25
                    
                    # Modifier description
                    desc_lines = self.wrap_text(mod_desc, self.fonts['info'], content_width - 60)
                    for line in desc_lines:
                        desc_text = self.fonts['info'].render(line, True, self.colors['TEXT_SECONDARY'])
                        screen.blit(desc_text, (content_x + 40, content_y))
                        content_y += 20
                    content_y += 10
            else:
                no_mods_text = self.fonts['menu'].render("No modifiers applied", True, self.colors['TEXT_SECONDARY'])
                screen.blit(no_mods_text, (content_x + 20, content_y))
                content_y += 30
        else:
            desc_title = self.fonts['subtitle'].render("Description", True, self.colors['TEXT_PRIMARY'])
            screen.blit(desc_title, (content_x, content_y))
            content_y += 35
            
            # Description text (wrap to multiple lines)
            description = metadata.get('description', 'No description available.')
            desc_lines = self.wrap_text(description, self.fonts['menu'], content_width - 40)
            for line in desc_lines:
                desc_text = self.fonts['menu'].render(line, True, self.colors['TEXT_SECONDARY'])
                screen.blit(desc_text, (content_x + 20, content_y))
                content_y += 25
        
        # Additional preview content would continue here...
        # (Level details, rewards, tips, etc.)
    
    def wrap_text(self, text: str, font, max_width: int) -> list:
        """Wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
