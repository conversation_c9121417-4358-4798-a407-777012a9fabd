"""
Main Launcher Coordinator for Tower Defense Game

Coordinates all the extracted components to provide the complete launcher functionality.
"""

import pygame
import sys
import time
from typing import Optional

from .configuration_manager import ConfigurationManager
from .navigation_manager import NavigationManager
from .event_handler import EventHandler
from .launcher_renderer import LauncherRenderer
from .game_launcher import GameLauncher


class MainLauncher:
    """Main launcher class that coordinates all components"""
    
    def __init__(self):
        """Initialize the main launcher"""
        pygame.init()
        
        # Screen setup
        self.SCREEN_WIDTH = 1000
        self.SCREEN_HEIGHT = 800
        self.screen = pygame.display.set_mode((self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense - Game Launcher")
        
        # Game loop settings
        self.clock = pygame.time.Clock()
        self.FPS = 60
        self.running = True
        
        # Initialize systems needed for configuration loading
        from game_systems.global_upgrade_system import GlobalUpgradeSystem
        from game_systems.global_upgrade_ui import GlobalUpgradeUI
        from game_systems.variant_selection_ui import VariantSelectionUI
        
        self.global_upgrade_system = GlobalUpgradeSystem()
        self.global_upgrade_ui = GlobalUpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)
        self.variant_ui = VariantSelectionUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        
        # Initialize component managers
        self.configuration_manager = ConfigurationManager()
        self.navigation_manager = NavigationManager()
        self.event_handler = EventHandler(self.navigation_manager, self.configuration_manager)
        self.launcher_renderer = LauncherRenderer(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.game_launcher = GameLauncher(self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)
        
        # Set up event handler callbacks
        self.event_handler.set_callback('generate_config', self.generate_adaptive_config)
        self.event_handler.set_callback('launch_game', self.launch_game)
        self.event_handler.set_callback('handle_variant_creation', self.handle_variant_creation)
        self.event_handler.set_callback('quit_application', self.quit_application)
        
        # Load configurations and performance stats
        self.configuration_manager.load_configurations()
        self.performance_stats = self.game_launcher.load_performance_stats()
        
        # Update scroll limits
        self.update_scroll_limits()
    
    def update_scroll_limits(self):
        """Update scroll limits based on current view"""
        cards_per_row = 3
        rows_visible = 4
        
        if self.navigation_manager.current_view == "main":
            total_configs = len(self.configuration_manager.configs)
        else:  # variants view
            base_level = self.navigation_manager.selected_base_level
            total_configs = len(self.configuration_manager.variants.get(base_level, [])) + 1  # +1 for back button
        
        self.navigation_manager.update_scroll_limits(total_configs, cards_per_row, rows_visible)
    
    def generate_adaptive_config(self):
        """Generate new adaptive configuration"""
        if not self.performance_stats:
            self.navigation_manager.show_status_message("❌ No performance data found. Play some games first!")
            return
        
        self.navigation_manager.show_status_message("🔄 Generating new level...")
        
        success = self.game_launcher.generate_adaptive_config(self.performance_stats)
        
        if success:
            avg_score = self.performance_stats['average_score']
            trend = self.performance_stats['trend']
            self.navigation_manager.show_status_message(f"✅ 🧩 AI level generated! Avg: {avg_score:.1f}%, Trend: {trend}")
            
            # Reload configurations and performance stats
            self.configuration_manager.load_configurations()
            self.performance_stats = self.game_launcher.load_performance_stats()
            self.update_scroll_limits()
        else:
            self.navigation_manager.show_status_message("❌ Failed to generate adaptive config")
    
    def launch_game(self, config_info):
        """Launch the game with selected configuration"""
        success = self.game_launcher.launch_game(config_info)
        
        if success:
            # Reinitialize display after game
            self.screen = self.game_launcher.reinitialize_display()
            if self.screen is None:
                self.running = False
                return
            
            # Reload configurations and performance stats (in case new ones were generated)
            self.configuration_manager.load_configurations()
            self.performance_stats = self.game_launcher.load_performance_stats()
            self.update_scroll_limits()
            
            # Check if we should auto-generate adaptive config
            if self.game_launcher.handle_post_game():
                self.auto_generate_adaptive_config()
        else:
            self.navigation_manager.show_status_message("❌ Failed to launch game")
    
    def auto_generate_adaptive_config(self):
        """Automatically generate adaptive config after game completion"""
        try:
            # Reload performance stats first
            self.performance_stats = self.game_launcher.load_performance_stats()
            
            if self.performance_stats:
                success = self.game_launcher.generate_adaptive_config(self.performance_stats)
                if success:
                    # Reload configurations
                    self.configuration_manager.load_configurations()
                    self.update_scroll_limits()
                    print("Auto-generated adaptive configuration after game completion")
        except Exception as e:
            print(f"Error in auto-generation: {e}")
    
    def handle_variant_creation(self):
        """Handle creation of a new variant"""
        try:
            variant = self.variant_ui.get_selected_variant()
            if variant:
                # The variant is already created and saved by the UI
                print(f"Variant created: {variant.variant_name}")
                print(f"Difficulty: {variant.total_difficulty_multiplier:.1f}x")
                print(f"Reward: {variant.total_reward_multiplier:.1f}x")
                print(f"Path: {variant.config_path}")
                
                # Close variant selector
                self.navigation_manager.close_variant_selector()
                self.variant_ui.close()
                
                # Reload configurations to include the new variant
                self.configuration_manager.load_configurations()
                self.update_scroll_limits()
                
                # Show status message
                self.navigation_manager.show_status_message(f"✅ Variant created: {variant.variant_name}")
                
        except Exception as e:
            print(f"Error creating variant: {e}")
            self.navigation_manager.show_status_message(f"❌ Error creating variant: {e}")
    
    def quit_application(self):
        """Quit the application"""
        self.running = False
    
    def handle_ui_overlays(self, events):
        """Handle UI overlays (variant selector, upgrade menu)"""
        if self.navigation_manager.show_variant_selector:
            action = self.variant_ui.handle_click(pygame.mouse.get_pos()) if any(e.type == pygame.MOUSEBUTTONDOWN and e.button == 1 for e in events) else None
            if action == "create_variant":
                self.handle_variant_creation()
            elif action == "close":
                self.navigation_manager.close_variant_selector()
            
            # Handle scrolling
            for event in events:
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 4:  # Mouse wheel up
                        self.variant_ui.handle_scroll(-1)
                    elif event.button == 5:  # Mouse wheel down
                        self.variant_ui.handle_scroll(1)
                elif event.type == pygame.MOUSEWHEEL:
                    self.variant_ui.handle_scroll(event.y)
        
        elif self.navigation_manager.show_upgrade_menu:
            for event in events:
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    self.global_upgrade_ui.handle_click(event.pos)
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 4:  # Mouse wheel up
                        self.global_upgrade_ui.handle_scroll(-1)
                    elif event.button == 5:  # Mouse wheel down
                        self.global_upgrade_ui.handle_scroll(1)
                elif event.type == pygame.MOUSEWHEEL:
                    self.global_upgrade_ui.handle_scroll(event.y)
    
    def run(self):
        """Main launcher loop"""
        while self.running:
            # Get events
            events = pygame.event.get()
            
            # Handle UI overlays first
            self.handle_ui_overlays(events)
            
            # Handle main events
            if not self.event_handler.handle_events(events):
                self.running = False
            
            # Clear visual feedback
            self.event_handler.clear_visual_feedback()
            
            # Draw everything
            self.draw()
            
            # Update display and tick clock
            pygame.display.flip()
            self.clock.tick(self.FPS)
            
            # Clear status message after 5 seconds
            if self.navigation_manager.show_generation_status:
                if not hasattr(self, '_status_timer'):
                    self._status_timer = time.time()
                elif time.time() - self._status_timer > 5:
                    self.navigation_manager.hide_status_message()
                    delattr(self, '_status_timer')
        
        pygame.quit()
        sys.exit()
    
    def draw(self):
        """Draw the launcher interface"""
        # Draw main interface
        self.launcher_renderer.draw_main_interface(
            self.screen, self.navigation_manager, self.configuration_manager,
            self.event_handler, self.performance_stats
        )
        
        # Draw UI overlays
        if self.navigation_manager.show_upgrade_menu:
            try:
                self.global_upgrade_ui.draw(self.screen)
            except Exception as e:
                print(f"Error drawing upgrade menu: {e}")
        
        # If upgrade menu was closed from within the UI, reset the flag
        if self.navigation_manager.show_upgrade_menu and not self.global_upgrade_ui.is_open:
            self.navigation_manager.show_upgrade_menu = False
        
        # Variant selector (if visible)
        if self.navigation_manager.show_variant_selector:
            self.variant_ui.draw(self.screen)
