"""
Game Launcher for Tower Defense Game

Handles the actual game launching and post-game processing.
"""

import os
import pygame
from typing import Dict, Any


class GameLauncher:
    """Handles game launching and post-game processing"""
    
    def __init__(self, screen_width: int, screen_height: int, global_upgrade_system):
        """Initialize the game launcher"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.global_upgrade_system = global_upgrade_system
    
    def launch_game(self, config_info: Dict[str, Any]) -> bool:
        """Launch the game with the selected configuration. Returns True if successful."""
        print(f"Launching game with config: {config_info['name']}")
        
        try:
            # Set the config file for the game to use
            from config.game_config import set_config_file
            set_config_file(config_info['path'])
            
            # Import and run the game
            from game import Game
            
            # Close launcher window
            pygame.display.quit()
            
            # Run the game (skip config selection since launcher already set it)
            game = Game(skip_config_selection=True, launched_from_menu=True)
            # Pass the global upgrade system to the game
            game.global_upgrade_system = self.global_upgrade_system
            game.run()
            
            # Game finished - check if we should generate adaptive config
            self.handle_post_game()
            
            # Reinitialize pygame for launcher
            pygame.display.init()
            
            return True
            
        except Exception as e:
            print(f"Error launching game: {e}")
            # Reinitialize pygame for launcher in case of error
            try:
                pygame.display.init()
            except:
                pass
            return False
    
    def handle_post_game(self):
        """Handle actions after game completion"""
        # Check for new performance data using completion marker
        performance_dir = os.path.join("ai", "performance_data")
        marker_path = os.path.join(performance_dir, "last_completion.txt")
        
        if os.path.exists(marker_path):
            try:
                # Check if this is a new completion since we last checked
                with open(marker_path, 'r') as f:
                    lines = f.read().strip().split('\n')
                    if len(lines) >= 2:
                        completion_timestamp = lines[0]
                        performance_filename = lines[1]
                        
                        # Check if we've already processed this completion
                        processed_marker_path = os.path.join(performance_dir, "last_processed.txt")
                        should_process = True
                        
                        if os.path.exists(processed_marker_path):
                            with open(processed_marker_path, 'r') as pf:
                                last_processed = pf.read().strip()
                                if last_processed == completion_timestamp:
                                    should_process = False  # Already processed this completion
                        
                        if should_process:
                            print(f"Detected new game completion at {completion_timestamp}")
                            print(f"Performance saved as: {performance_filename}")
                            
                            # Mark this completion as processed
                            with open(processed_marker_path, 'w') as pf:
                                pf.write(completion_timestamp)
                            
                            # Return True to indicate new completion was detected
                            return True
                        else:
                            print("No new game completions detected")
                            
            except Exception as e:
                print(f"Error checking completion marker: {e}")
        else:
            print("No completion marker found - no recent game completions")
        
        return False
    
    def generate_adaptive_config(self, performance_stats: Dict = None) -> bool:
        """Generate adaptive config based on recent games. Returns True if successful."""
        try:
            print("Starting adaptive config generation...")
            from ai.adaptive_config_generator import AdaptiveConfigGenerator
            
            if not performance_stats:
                print("No performance data found")
                return False
            
            print(f"Generating adaptive config from {performance_stats['games_count']} recent games...")
            
            # Generate adaptive config using multi-game analysis with Modular AI
            generator = AdaptiveConfigGenerator(use_modular_ai=True, use_full_ai=False)
            config = generator.generate_config_from_recent_games()
            
            if config:
                avg_score = performance_stats['average_score']
                trend = performance_stats['trend']
                method = config.get('_adaptive_metadata', {}).get('generation_method', 'unknown')
                print(f"Generated config with method: {method}")
                print(f"Multi-game adaptive configuration generated (Avg: {avg_score:.1f}%, Trend: {trend})")
                return True
            else:
                print("Generator returned None - generation failed")
                return False
                
        except Exception as e:
            print(f"Error generating adaptive config: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def load_performance_stats(self) -> Dict:
        """Load and analyze recent performance statistics"""
        try:
            from ai.adaptive_config_generator import load_all_recent_performances
            
            # Load recent performances
            performances = load_all_recent_performances()
            
            if not performances:
                return None
            
            # Calculate aggregate statistics
            scores = [p.score for p in performances]
            avg_score = sum(scores) / len(scores)
            win_rate = sum(1 for p in performances if p.win_flag) / len(performances) * 100
            
            # Get performance trend (improving/declining)
            trend = "stable"
            if len(scores) >= 3:
                recent_avg = sum(scores[:2]) / 2  # Last 2 games
                older_avg = sum(scores[2:]) / len(scores[2:])  # Older games
                if recent_avg > older_avg + 10:
                    trend = "improving"
                elif recent_avg < older_avg - 10:
                    trend = "declining"
            
            # Create performance history for display
            performance_history = []
            for i, perf in enumerate(performances):
                performance_history.append({
                    'game_number': i + 1,
                    'score': perf.score,
                    'won': perf.win_flag,
                    'lives_remaining_pct': (perf.lives_remaining / perf.starting_lives) * 100,
                    'towers_used': len([t for t, c in perf.towers_built.items() if c > 0])
                })
            
            return {
                'games_count': len(performances),
                'average_score': avg_score,
                'win_rate': win_rate,
                'trend': trend,
                'performance_history': performance_history,
                'latest_score': scores[0] if scores else 0
            }
            
        except Exception as e:
            print(f"Error loading performance stats: {e}")
            return None
    
    def reinitialize_display(self):
        """Reinitialize pygame display after game"""
        try:
            screen = pygame.display.set_mode((self.screen_width, self.screen_height))
            pygame.display.set_caption("Tower Defense - Game Launcher")
            return screen
        except Exception as e:
            print(f"Error reinitializing display: {e}")
            return None
