#!/usr/bin/env python3
"""
Tower Defense Game Launcher
Foundation system for choosing configurations and automatic level generation

This file now uses the modular launcher system for better organization and maintainability.
"""

import sys
from launcher import MainLauncher


def main():
    """Main entry point using the new modular launcher system"""
    print("=== Tower Defense Game Launcher ===")
    launcher = MainLauncher()
    launcher.run()


if __name__ == "__main__":
    main()